<?php
use App\Http\Models\APICommonparser;

if (!function_exists('generateLanguageDependentData')) {

    function generateLanguageDependentData($table,$existRecords=array())
    {
       
        

    }
}
if (!function_exists('prepareFieldsParam')) {

    function prepareFieldsParam($table)
    {
       

    }

}

function getImgUrl($path){
    $imgBaseUrl = \Config::get('app.MIX_CDN_URL');
    if($imgBaseUrl){
        return \Config::get('app.MIX_CDN_URL').$path;
    }else{
        return \Config::get('app.MIX_BACK_CDN_URL').$path;
    }
}

if(!function_exists('triggerSeoUrls'))
{
    function triggerSeoUrls($id,$type,$data)
    {
        if(!empty($id) && !empty($type) && !empty($data))
        {
            // $slug= str_slug($data.' '.$id,'-');
            if(!empty($data))
            {
                // echo $id;
                // echo $type;
                // echo $data;
                //find if name exists
                $records = \App\Http\Models\Seo::where('object_data',$data)->where('object_type',$type)->where('object_id','!=',$id)->get();
                // p($records);
                if(!$records->isEmpty())
                {
                  $data = $data.'-'.$id;
                }

                 $slug = App\Http\Models\Seo::updateOrCreate(['object_type'=>$type,'object_id'=>$id],
                        ['object_type'=>$type,'object_id'=>$id,'object_data'=>$data]);
           return $slug;
            }
          

        }
       
    }
} 

if (!function_exists('p')) {
function p($data)
{
    echo '<pre>';print_r($data);die;
}
}

if (!function_exists('addLanguageObject')) {

    function addLanguageObject($data,$key,$id)
    {
       

    }
}

if (!function_exists('buildTree')) {

function buildTree(array $elements, $parentId = 0,$idKey='id',$parentKey='parent_id') {
    
    $branch = array();

    foreach ($elements as $element) 
    {
       // error_log('parent_id : '.$parentId. ' record key: '.$element[$parentKey]. 'condition: '.($element[$parentKey] == $parentId));
        if (isset($element[$parentKey]) && $element[$parentKey] == $parentId) 
        {
            //error_log('key : '.$element[$idKey]);
            $children = buildTree($elements, $element[$idKey]);
            
            if ($children) 
            { 
                //error_log('inside children: '.$parentId);
                $element['children'] = $children;
            }
            
            $branch[] = $element;
        }
        elseif(!isset($element[$parentKey]))
        {
             $branch[] = $element;
        }
    }

    return $branch;
}

}

if (!function_exists('prepareTreeDropdown')) {
function prepareTreeDropdown($array=array(),$level=0)
{
    foreach($array as $node)
    {
        echo '<option value="'.$node['id'].'">'.str_repeat("-", $level).$node['title'].'</option>';
        if(isset($node['children'])) {
            //echo '<br/>';
            prepareTreeDropdown($node['children'], $level + 1);
           //echo str_repeat("&emsp;", $level);
        }
        //echo "</option>", '<br>';
        
    }
}
}

if (!function_exists('parentChildDropdown')) {
function parentChildDropdown($array=array(),$level=0)
{
    foreach($array as $node)
    {   
        if(!isset($node['children']) && $node['parent_id'] == 0) {
             echo '<option value="'.$node['id'].'">'.$node['title'].'</option>';
        }
        if(isset($node['children'])) {
            foreach($node['children'] as $child){
                echo '<option value="'.$child['id'].'">'.$node['title'].'-'.$child['title'].'</option>';
            }
        }
        //echo "</option>", '<br>';
        
    }
}
}

//parse multilevel tree in hierarchy form
if (!function_exists('parseTree')) {
    function parseTree($tree, $root = 0) 
    {
        //print_r($tree);
        $return = array();
        # Traverse the tree and search for direct children of the root
        foreach($tree as $child => $parent) 
        {
            # A direct child is found
            //$parent = get_object_vars($parent);
            
            if(is_object($parent))
              $parent = get_object_vars($parent);  


           
            if($parent['parentid'] == $root) {
                # Remove item from tree (we don't need to traverse this again)
                unset($tree[$child]);
                # Append the child into result array and parse its children
                $return[$parent['categoryid']] = array(
                    'name' => $parent,
                    'children' => parseTree($tree, $parent['categoryid'])
                );
            }
        }
        return empty($return) ? null : $return;    
    }

}

//get prefix assigned in url
if (!function_exists('getCurrentUrlPrefix')) {

    function getCurrentUrlPrefix()
    {
         $string = \Route::current()->uri();
         $prefix = \Route::current()->getAction()['prefix'];

         // return $prefix;
        if(!empty($prefix))
         {
            $stringC = explode('/', $string);
           if(count($stringC) > 2)
            $urlprefix = $stringC[0].'/'.$stringC[1];
            else
            $urlprefix = $string;    
        }
        else
        {
            $urlprefix = $string;
        }
        return $urlprefix;
            
    }

}


//get system level prefix
if (!function_exists('getPrefix')) {

    function getPrefix()
    {
         $string = \Route::current()->uri();
         $prefix = \Route::current()->getAction()['prefix'];
         return $prefix;

         
            
    }

}


//convert any object to array based on key
if (!function_exists('objToArray')) {
    function objToArray($array = array(), $key = '') {
        $var = array();
        if (isset($array) and !empty($array)) {
            foreach ($array as $arr) {
                if(is_object($arr))
                $arr = get_object_vars($arr);
                if(!isset($var[$arr[$key]]))
                {$var[$arr[$key]] = $arr;}
            }
        }
        return $var;
    }

}


//need to format number input - reformatting comma
if (!function_exists('makecomma')) {
function makecomma($input)
{
    // This function is written by some anonymous person - I got it from Google
    if(strlen($input)<=2)
    { return $input; }
    $length=substr($input,0,strlen($input)-2);
    $formatted_input = makecomma($length).",".substr($input,-2);
    return $formatted_input;
}
}


//replace few of text with asterisk / masking text
if (!function_exists('asterisks')) {
function asterisks($toConvert) {
    $astNumber = strlen($toConvert) - 2;
    if($astNumber<0){
        $astNumber = 0;
     }
    return substr($toConvert, 0, 1) . str_repeat("*", $astNumber) . substr($toConvert, -1);
}
}


if (!function_exists('formatInIndianStyle')) {
function formatInIndianStyle($num){
    // This is my function
    $pos = strpos((string)$num, ".");
    if ($pos === false) { $decimalpart="00";}
    else { $decimalpart= substr($num, $pos+1, 2); $num = substr($num,0,$pos); }

    if(strlen($num)>3 & strlen($num) <= 12){
                $last3digits = substr($num, -3 );
                $numexceptlastdigits = substr($num, 0, -3 );
                $formatted = makecomma($numexceptlastdigits);
               // $stringtoreturn = $formatted.",".$last3digits.".".$decimalpart ;
                $stringtoreturn = $formatted.",".$last3digits ;
    }elseif(strlen($num)<=3){
                //$stringtoreturn = $num.".".$decimalpart ;
                $stringtoreturn = $num;
    }elseif(strlen($num)>12){
                $stringtoreturn = number_format($num, 2);
    }

    if(substr($stringtoreturn,0,2)=="-,"){$stringtoreturn = "-".substr($stringtoreturn,2 );}

    return $stringtoreturn;
}
}

//prepare menu as per module prepared in admin
if (!function_exists('modulesgroup')) {
function modulesgroup($data,$flag=0)
{
    $menuarray = array();
    if(!empty($data))
    {
        //print_r($data);
        foreach ($data as $key => $value) 
        {
          # code...
          if($flag==0 && $value['menu_name']!='' && $value['is_access']=='Y')
          $menuarray[$value['menu_group_position']][] = $value;
          elseif($value['menu_type'] >=0 && $flag==1)
          @$menuarray[$value['menu_group_position']][] = $value;      

          
        }


    }
    ksort($menuarray);

    //echo '<pre>';print_r(array_values($menuarray));die;
        $revisedArray = array();
     foreach ($menuarray as $key => $value) 
        {
          # code...
            // print_r($value);die;
            foreach ($value as $k => $v) {
                # code...
                // print_r($v); die;
                 if($flag==0 && $v['menu_name']!='' && $v['is_access']=='Y')
                  $revisedArray[$v['menu_group']][] = $v;
                  elseif($v['menu_type'] >=0 && $flag==1)
                  $revisedArray[$v['menu_group']][] = $v;    
            }
           

          
        }

        // echo '<pre>sdsd';print_r(($revisedArray));die;
    // $revisedArray = array();

    // //revised menu array
    // foreach ($menuarray as $key => $value) {
    //     # code...
    //     $revisedArray[$value]
    // }
    return ($revisedArray);
}
}

//hit multi curl and get response
if (!function_exists('getCurlResponse')) {
 function getCurlResponse($modules,$getData=array(),$postData=array()) 
{
    if (isset($argv[1])) {
                    $max_requests = $argv[1];
                } else {
                    $max_requests = 10;
                }
                $curl_options = array(
                    CURLOPT_SSL_VERIFYPEER => FALSE,
                    CURLOPT_SSL_VERIFYHOST => FALSE,
                    CURLOPT_USERAGENT, 'Parallel Curl test script',
                );
                $parallel_curl = new App\library\ParallelCurl($max_requests, $curl_options);
                $collector = new App\library\ReturnCollector();

                $requestList = $modules['modules'];
                //echo '<pre>';print_r($requestList); die;
                foreach ($requestList as $key=>$request) {


                    if(isset($request['url']) and !empty($request['url']))
                    {
                       // if (\Cache::has($key) and 1==2)
                        $cachekey = sha1($request['url']);
                        if (\Cache::has($cachekey) and $request['method']=='GET' and $request['cache']==true)
                        {
                            
                            $collector->addCacheData(\Cache::get($cachekey),$cachekey);
                           // echo 'coming from cache\n';
                        }
                        else
                        {
                            if($request['method']=='GET')
                            {
                                //$parallel_curl->startRequest($request['url'],array($collector,'addData'),$key);
                                if(!empty($getData))
                                {
                                    $url = prepareUrl($request['url'],$getData);
                                }
                                else
                                {
                                    $url = $request['url'];
                                }
                                $parallel_curl->startRequest($url,array($collector,'addData'),array('module'=>$key,'cache'=>@$request['cache'],'cache_interval'=>@$request['cache_interval'],'headers'=>@$request['headers']));
                            }
                            elseif($request['method']=='POST')
                            { 
                                $parallel_curl->startRequest($request['url'],array($collector,'addData'),array('module'=>$key,'cache'=>@$request['cache'],'cache_interval'=>@$request['cache_interval'],'headers'=>@$request['headers']),$request['params']);
                                //echo '<pre>';print_r($request);die;
                            }
                            elseif($request['method']=='PUT')
                            { 
                                $parallel_curl->startRequest($request['url'],array($collector,'addData'),array('module'=>$key,'cache'=>@$request['cache'],'cache_interval'=>@$request['cache_interval'],'headers'=>@$request['headers']),$request['params'],'PUT');
                               //echo '<pre>';print_r($request);die('end');
                            }
                        }
                        
                    }
                   
                }
                // This should be called when you need to wait for the requests to finish.
                // This will automatically run on destruct of the ParallelCurl object, so the next line is optional.
                $parallel_curl->finishAllRequests();
                return $collector->outputData();
                
                 
}
}

//dependent function of getCurlResponse
if (!function_exists('on_request_done')) {
function on_request_done($content, $url, $ch, $search) {
    
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);    
    if ($httpcode !== 200) {
       // print "Fetch error $httpcode for '$url'\n";
        //return;
        return array('error'=>$httpcode,'url'=>$url);
    }
    else
    {
        $responseobject = json_decode($content, true);
        return $responseobject;
    }
}
}


//short form to translate text using language intelligence
if (!function_exists('lang')) {
function lang($string,$file='')
{
    if($file==''){$file='common';}

    return \Lang::get($file.'.'.$string);
}  
}

//standard function to upload image and return image path
if (!function_exists('uploadImage')) {

    function uploadImage($file,$directory)
    {
        $img_path = ' ';
        if(!empty($file))
            {
                # code...
                    // $imgname = date('Ymd').'_'.date('His').'_'.str_random(5).'_'.$file->getClientOriginalName();
                    //$img_path = Storage::putFile('cdn/'.$directory, $file);
                    // $import_storage = $file->move('./properties/'.$directory.'/', $imgname);   
                    // $img_path = '/properties/'.$directory.'/'.$imgname;

                    $filepath = '/storage/import/'.$directory.'/';
                    //Check if the directory already exists.
                        if(!is_dir('.'.$filepath)){
                            //Directory does not exist, so lets create it.
                            $path = public_path().$filepath;
                            File::makeDirectory($path, $mode = 0777, true, true);
                            
                        }
                    $filename = date('Ymd').'_'.date('His').'_'.str_random(5).'.'.$file->getClientOriginalExtension();
                    $import_storage = $file->move('.'.$filepath, $filename);

                    return $filepath.$filename;
                    

            }
            // return $img_path;
    }


    }

 

//generate api key for based on logged in user
if(!function_exists('getApiKey'))
{
     function getApiKey()
    {
        //die('cming');

        $user_id = @auth()->guard('user')->user()->id;
        if(!empty($user_id))
        {
            $record = new App\Http\Models\Accesstoken;
            $record->access_token = md5(base64_encode(date('ymd').$user_id).str_random(10));  
            $record->user_id = $user_id;   
            $record->expiry_date = strtotime(date('Y-m-d H:i:s', strtotime('+30 days')));   
            $record->save();

            session(['api_key'=>$record->access_token]);
            return $record->access_token;
        }

        
       
    } 
}  


//verify whether eloquent query is parsed fine or not
if(!function_exists('validateEloquentQuery'))   
{

    function validateEloquentQuery($data)
    {
        $response = array();
        if(!empty($data))
        {
            foreach ($data as $key => $value) {
                # code...
                if(empty($value))
                    $value = '';

                $response[$key]=$value;
            }
        }
        return $response;

    }


} 


if(!function_exists('checkEnv'))   
{

    function checkEnv($response)
    {
        $appenv = \Config::get('app.debug');
        if($appenv==false)
        {
         $response['modules'] = array();
         $response['headers'] = array();
         $response['params'] = array();
        }
        
        return $response;

    }


} 


if(!function_exists('generatePIN'))   
{
//Our custom function.
function generatePIN($digits = 4){
    $i = 0; //counter
    $pin = ""; //our default pin is blank.
    while($i < $digits){
        //generate a random number between 0 and 9.
        $pin .= mt_rand(0, 9);
        $i++;
    }
  
  return $pin;
}
}

function generateUniqueKey($length = 8)
{
  $random = "";
  srand((double) microtime() * 1000000);

  $data = "123456123456789071234567890890";
  $data .= "aBCdefghijklmn123opq45rs67tuv89wxyz"; // if you need alphabatic also

  for ($i = 0; $i < $length; $i++) {
          $random .= substr($data, (rand() % (strlen($data))), 1);
  }

  return strtoupper($random);

}

function getDropdownByValue($response=array())
{
  if(!empty($response))
  {
    echo '<option value="">Select </option>';
    foreach ($response as $key => $value) {
      # code...
      echo '<option value="'.$value.'">'.$value.'</option>';
    }
  }

}

//create select dropdown by key value array
function getDropdownByKValue($response=array())
{
  if(!empty($response))
  {
    echo '<option value="">Select </option>';
    foreach ($response as $key => $value) {
      # code...
      echo '<option value="'.$key.'">'.$value.'</option>';
    }
  }

}

function sendMails($options=array())
{
    if(config('app.env')=='production')
    {
            \Mail::send(['html'=>'emails.'.@$options['template']], 
                ['results' => @$options['results']], 
                function($message) use($options) {
                    // print_r($options);die;
                    //$options['to'] = array_merge($options['to'],'<EMAIL>');
                    $message->to($options['to']);
                    if(isset($options['bcc']))
                    {$message->bcc($options['bcc']);}
                    $message->subject($options['subject']);
                    $message->from(config('mail.from.address'),config('mail.from.name'));
                                  
                }); 
    }
    
}


function checkCacheAvailability($key)
{
    $collector = new App\library\CacheManager();
    $cacheData = $collector->getData($key);
    // p($cacheData);
    if($cacheData!=false)
    {
        return $cacheData;
    }
}
function storeCacheStock($key,$data,$duration)
{
    $collector = new App\library\CacheManager();
    $cacheData = $collector->addData($key,$data,array('cache_interval'=>$duration));
    return $cacheData;exit;
}

function storeOrUpdateCache($key,$data,$duration)
{
    $collector = new App\library\CacheManager();
    $cacheData = $collector->addData($key,json_encode($data),array('cache_interval'=>$duration));
    return $cacheData;
}

function is_axios()
{
    $headers = Request::header();
    setLog($headers,'request type');

    if(isset($headers['axios_request'][0]) && $headers['axios_request'][0]==true)
    {
        return true;
    }
    elseif(isset($headers['radxsoft'][0]) && $headers['radxsoft'][0]=='labs')
    {
        return true;
    }
}

function setLog($message,$type = 'LOG TYPE'){
    if (config('app.debug')) {
        \Log::info('LOG '.@json_encode($type).'::'.@json_encode($message));
    }
}

function laravel_error_parser($inputs)
{
   
    $newformat = '';
    if(!empty($inputs))
    {
        $newformat = '<ul>';
        foreach ($inputs as $key => $value) {
            # code...
            $newformat .= '<li>'.$value[0].'</li>';
        }
        $newformat .= '</ul>';
    }

     return $newformat;
}

/* strpos that takes an array of values to match against a string
 * note the stupid argument order (to match strpos)
 */
function strpos_arr($haystack, $needle) {
    if(!is_array($needle)) $needle = array($needle);
    foreach($needle as $what) {
        if(($pos = strpos($haystack, $what))!==false) return $pos;
    }
    return false;
}

function slugify($text='')
{
  // replace non letter or digits by -
  $text = preg_replace('~[^\pL\d]+~u', '-', $text);

  // transliterate
  $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

  // remove unwanted characters
  $text = preg_replace('~[^-\w]+~', '', $text);

  // trim
  $text = trim($text, '-');

  // remove duplicate -
  $text = preg_replace('~-+~', '-', $text);

  // lowercase
  $text = strtolower($text);

  if (empty($text)) {
    return 'n-a';
  }

  return $text;
}

function sendSms($destNo, $otp){
        $curl = curl_init();

        curl_setopt_array($curl, array(

            #old one
         // CURLOPT_URL => "https://instaalerts.zone/SendSMS/sendmsg.php?uname=QUREKAOTPUNICEL&pass=x5N3Y5Z%29&send=Qureka&dest=".$destNo."&msg=%3C%23%3E+Dear+User%5CnYour+OTP+Code+%3A+".$otp."%5CnDo+not+share+this+OTP+with+anyone+for+security+reasons.+Happy+quizzing%21%5Cn-+Qureka%5CnAjsdfwe23",

            # new  url
           CURLOPT_URL => "https://instaalerts.zone/SendSMS/sendmsg.php?uname=QUREKAOTPUNICEL&pass=x5N3Y5Z)&send=Qureka&dest=".$destNo."&dlt_entity_id=110100001549&dlt_template_id=1107160269209508310&msg=%3C%23%3E+Dear+User%5CnYour+OTP+Code+%3A+".$otp."%5CnDo+not+share+this+OTP+with+anyone+for+security+reasons.+Happy+quizzing!%5Cn-+Qureka%5CnAjsdfwe23",
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => "",
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 30,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => "GET",
          CURLOPT_HTTPHEADER => array(
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: f150b381-2dca-4b85-a6b3-9fd343cf4a33",
            "cache-control: no-cache"
          ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
          // echo "cURL Error #:" . $err;
        } else {
          return $response;
        }
   // p($modulesResponse);
}

function RemoveFalseButNotZero($value) {
  return ($value || is_numeric($value));
}

function solrClient($endpoint='')
{
    $adapter = new \Solarium\Core\Client\Adapter\Curl();
     $eventDispatcher = new \Symfony\Component\EventDispatcher\EventDispatcher;
     $config = \Config('solarium');
   
     $a = new \Solarium\Client($adapter,$eventDispatcher,$config);
     return $a;
}

  function getDomainReferralDetails(){
        $apache_headers= apache_request_headers();
        $servers = $_SERVER;

        $referer = (@$apache_headers['Referer']) ? (@$apache_headers['Referer']) : (@$servers['HTTP_REFERER']);
        $app_source = (@$apache_headers['X-Requested-With']) ? (@$apache_headers['X-Requested-With']) : (@$servers['HTTP_X_REQUESTED_WITH']);
        $domain = (@$apache_headers['Host']) ? (@$apache_headers['Host']) : (@$servers['HTTP_HOST']);

       // $refererValue = 'blank';
	  $refererValue = '';
	
        if(!empty($referer))
        {
            $referer = preg_replace( "#^[^:/.]*[:/]+#i", "", $referer );
            $referer = explode('/',$referer);
            $referer = @$referer[0];
            if($referer != $domain) {
                $refererValue = $referer;
		return $refererValue.'|'.$app_source;
            }
        }else{
 		return $refererValue;
	}

        
 }
 function getTagWiseCategories($tagId)
{
    $data = objToArray(\DB::table('categories')->select('id','title','parent_id')->where('parent_id', '!=', 0)->where('tags', $tagId)->get()->toArray(),'id');
    return $data;

}

function call_login_register($email, $password, $fullName){
    try {
        $loginModules = \Config::get("aireadiness.apis.signup");
        $apiUrl = $loginModules;

        $response = Http::post($apiUrl, [
            'email' => $email,
            'password' => $password,
            'full_name' => $fullName,
        ]);

        if ($response->successful()) {
            $data = $response->json();

            if (isset($data['access_token']) && !empty($data['expires_in'])) {
                $expiresIn = (int)$data['expires_in']; // in minute

                return [
                    'status' => 1,
                    'access_token' => $data['access_token'],
                    'expiresIn' => $expiresIn
                ];
            }
        }

        \Log::error('API response error: ' . $response->body());
    } catch (\Exception $e) {
        \Log::error('API call failed: ' . $e->getMessage());
    }

    return null;

}

function getWithBearerToken($url, $accessToken, $queryParams = [])
{
    try {
        $response = Http::withToken($accessToken)
                        ->acceptJson()
                        ->get($url, $queryParams);

        if ($response->successful()) {
            return $response->json();
        }

        \Log::error('API GET failed', [
            'url' => $url,
            'status' => $response->status(),
            'body' => $response->body()
        ]);
    } catch (\Exception $e) {
        \Log::error('API GET exception: ' . $e->getMessage(), ['url' => $url]);
    }

    return null;
}

function postWithBearerToken($url, $accessToken, $data = [])
{
    try {
        $response = Http::withToken($accessToken)
                        ->acceptJson()
                        ->post($url, $data);

        if ($response->successful()) {
            return [
                'status' => $response->status(),
                'data' => $response->json()
            ];
        }

        \Log::error('API POST failed', [
            'url' => $url,
            'status' => $response->status(),
            'body' => $response->body()
        ]);

        return [
            'status' => $response->status(),
            'data' => $response->json()
        ];
    } catch (\Exception $e) {
        \Log::error('API POST exception: ' . $e->getMessage(), ['url' => $url]);

        return [
            'status' => null,
            'data' => null
        ];
    }
}

?>
