@extends('cms::layout')

@section('title', 'TechGig | Assessment Report')
@section('content')

            <!-- Main Content -->
            <main role="main" class="assessment-content">

                <h1>Your Assessment Report</h1>
                <div class="report-container">
                    <img src="{{getImgUrl('/img/report-bg.png')}}" class="report-desktop" alt="">
                    <img src="{{getImgUrl('/img/report-bg-mobile.png')}}" class="report-mobile" alt="">
                    <div class="report">
                        <h3>{{ @$assessmentReportDetail['report']['total_score'] ? @$assessmentReportDetail['report']['total_score'] : '0' }}</h3>
                        <h4>You scored <strong><span>{{ @$assessmentReportDetail['report']['total_score'] ? @$assessmentReportDetail['report']['total_score'] : '0' }}/100</span> points</strong></h4>
                        <h5>{{ @$assessmentReportDetail['report']['readiness_tier'] ? @$assessmentReportDetail['report']['readiness_tier'] : '' }}</h5>
                    </div>
                </div>
                <div class="label-container">
                    <div class="label-box">
                        <div class="tag curious">AI Curious</div>
                        <div class="range">40-59%</div>
                    </div>
                    <div class="label-box">
                        <div class="tag ready">AI Ready</div>
                        <div class="range">60-79%</div>
                    </div>
                    <div class="label-box">
                        <div class="tag beginner">AI Beginner</div>
                        <div class="range">Below 40%</div>
                    </div>
                    <div class="label-box">
                        <div class="tag pioneer">AI Pioneer</div>
                        <div class="range">80-100%</div>
                    </div>
                </div>
                <div class="assessment-container">

                    <!-- Tabs -->
                    <div class="tabs">
                        <div class="tab active" data-tab="summary">Assessment Summary</div>
                        <div class="tab" data-tab="performance">Your Performance</div>
                    </div>

                    <!-- Tab Content 1 -->
                    <div id="summary" class="tab-content active">
                        <h2>Your Performance Summary</h2>
                        <p>
                        {{ is_array(@$assessmentReportDetail['report']['improvement_areas']['details']) 
                        ? implode('  ', @$assessmentReportDetail['report']['improvement_areas']['details']) 
                        : '' }}
                            <ul class="report_list">
                            @foreach($assessmentReportDetail['report']['improvement_areas']['points'] ?? [] as $points)
                                <li>{{ $points }}</li>
                            @endforeach
                            </ul>
                        </p>

                        <p>
                        {{ is_array(@$assessmentReportDetail['report']['strengths']['details']) 
                        ? implode('  ', @$assessmentReportDetail['report']['strengths']['details']) 
                        : '' }}
                            <ul class="report_list">
                            @foreach($assessmentReportDetail['report']['strengths']['points'] ?? [] as $points)
                                <li>{{ $points }}</li>
                            @endforeach
                               
                            </ul>
                        </p>

                        <h2>Recommended Learning Path</h2>
                        <p>
                        {{ is_array(@$assessmentReportDetail['report']['recommended_learning_path']['details']) 
                        ? implode('  ', @$assessmentReportDetail['report']['recommended_learning_path']['details']) 
                        : '' }}
                            <ul class="report_list">
                            @foreach($assessmentReportDetail['report']['recommended_learning_path']['points'] ?? [] as $points)
                                <li>{{ $points }}</li>
                            @endforeach
                            </ul>
                        </p>
                        

                        

                        <!--<div class="card-container">
                            <div class="card">
                                <h4>Lorem Ipsum</h4>
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam magna erat, convallis
                                    a dignissim quis,</p>
                                <a href="{{ url('lets-start') }}" class="btn">RETAKE ASSESSMENT</a>
                            </div>
                            <div class="card">
                                <h4>Lorem Ipsum</h4>
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam magna erat, convallis
                                    a dignissim quis,</p>
                                <a href="" class="btn">CREATE RESUME</a>
                            </div>
                        </div>-->
                    </div>

                    <!-- Tab Content 2 -->
                    <div id="performance" class="tab-content">
                        
                            
                            @foreach (@$assessmentReportDetail['questionsList'] as $questionKey => $question)
                            @php
                                $questionClass = '';
                                $questionText = '';
                                if ($question['is_correct'] == "1") {
                                    $questionClass = 'correct';
                                    $questionText = 'correct';
                                } 
                                else{
                                    if ($question['user_answer'] == "SKIP") {
                                        $questionClass = 'incorrect';
                                        $questionText = 'Skip';
                                    }
                                    else{
                                        $questionClass = 'incorrect';
                                        $questionText = 'incorrect';
                                    }
                                }

                            @endphp
                            <div class="assessment-output-box">
                                <div class="assessmet-question-header">
                                    <h2>Question {{$questionKey+1}}/{{count($assessmentReportDetail['questionsList'])}}:</h2>
                                    <span class="badge-status {{ $questionClass }}">{{ucfirst($questionText)}}</span>
                                </div>
                                <p class="question-text">
                                    {{ $question['question'] }}
                                </p>
                                @if ($question['question_type'] == "MCQ")
                                <div class="options-wrapper">
                                @foreach ($question['answer']['options'] as $key => $option)
                                    @php
                                        $class = '';
                                        if ($question['correct_answer'] == $key && $question['user_answer'] == $key) {
                                            $class = 'correct';
                                        } elseif ($question['user_answer'] == $key) {
                                            $class = 'incorrect';
                                        } elseif ($question['correct_answer'] == $key) {
                                            $class = 'correct';
                                        }

                                    @endphp
                                    <div class="option-card {{ $class }}">
                                        <div class="option-label">{{ $key }}</div>
                                        {{ $option }}
                                    </div>
                                @endforeach
                                </div>
                                @elseif ($question['question_type'] == "CODING")
                                <div class="yourcode-wrapper">
                                <h6>
                                    Your Code
                                </h6>
                                <div class="yourcode-box">
                                    <pre style="text-wrap-mode: wrap;">{{ @$question['user_answer'] }}</pre>
                                </div>
                            </div>

                            <div class="explanation-box">
                                <h4>Correct Answer:</h4>
                                <p>
                                <pre style="text-wrap-mode: wrap;">{{ @$question['correct_answer'] }}</pre>
                                </p>
                            </div>
                                @endif
                            </div>
                            @endforeach

                            
                        </div>
                        
                </div>
            </main>
            <script>
            const tabs = document.querySelectorAll('.tab');
            const contents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active from all tabs & contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));

                    // Activate clicked tab & related content
                    tab.classList.add('active');
                    document.getElementById(tab.dataset.tab).classList.add('active');
                });
            });
        </script>
@endsection