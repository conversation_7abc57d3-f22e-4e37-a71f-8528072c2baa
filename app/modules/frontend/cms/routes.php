<?php
use UniSharp\LaravelFilemanager\Middlewares\CreateDefaultFolder;
use UniSharp\LaravelFilemanager\Middlewares\MultiUser;

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It's a breeze. Simply tell <PERSON><PERSON> the URIs it should respond to
| and give it the Closure to execute when that URI is requested.
|
*/

//die('amgetting loaded');
//array('xssprotection','frameguard','admin')
Route::group(['module'=>'cms','namespace' => 'App\modules\frontend\cms\Controllers','middleware'=>['web']], function() {
			Route::group(['prefix'=>'api','middleware'=>[]], function() {

				Route::get('logout', 'SocialController@logout');
				Route::post('sociallogin/{provider}', 'SocialController@SocialSignup');
				Route::get('auth/{provider}/callback', 'SocialController@handleProviderCallback');
			});

			Route::get('readiness/api/auth/{provider}/callback', 'SocialController@handleProviderCallback');

			//enable regular pages
			Route::get('/', 'AIController@getBase')->name('home');
			Route::get('/signup', 'AIController@getSignin')->name('login');
			Route::group(['middleware'=>['socialauth']], function() {
				Route::get('/lets-start', 'AIController@letsStart');
				Route::post('/lets-start', 'AIController@postLetsStart'); 
				Route::get('/lets-start/{step}', 'AIController@stepForm');
				Route::post('/lets-start/{step}', 'AIController@postStepForm');
				//Route::get('/assessment-report/{id}', 'AIController@assessmentReport');
			});
			Route::get('oauth/{driver}', 'SocialController@redirectToProvider')->name('social.oauth');
			Route::get('/logout', 'AIController@logout');
			Route::get('/assessment-report/{id}', 'AIController@assessmentReport');


                Route::get('{any}', 'AIController@pageNotFound')->where('any', '^(?!api).*$');
        }); 