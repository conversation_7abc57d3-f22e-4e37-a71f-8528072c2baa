<?php 
namespace App\modules\frontend\cms\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Http\Models\Commonparser;
use App\Http\Models\Game;
use App\Http\Models\Contest;
use App\Http\Models\User;
use App\Http\Models\Contestwinner;
use App\Http\Models\Userwallettranshistory;
use App\Http\Models\Userwalletcoin;
use Carbon\Carbon;
// use Request;
use Validator;
use Input;
use Redirect;
use DB;
use Auth;
use Cookie;
use Session;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\Request;

class AIController extends Controller
{

    /**14583
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __construct()
    {
    }

    public function getHomePage()
    {
        return 'home page';
    //     $ext = pathinfo(\Request::getRequestUri(), PATHINFO_EXTENSION);
    //     if(in_array($ext, array('jpg','png','jpeg','woff2','css','js','map')))
    //     {
    //         exit;
    //     }


    //    // Cookie::queue('sociallogin', base64_encode(json_encode($getSocialCredential)), 604800,"","","",false);

    //     Session::put('sociallogin', base64_encode(json_encode($getSocialCredential)));
        

    //     setLog('request is coming!');
    //     $response = array();
        
    //     return view('cms::cms.home')->with($response);exit;
    }

    public function getBase()
    {
        return view('cms::landing1');
    }
    public function getSignin()
    {
        return view('cms::presignup.Signup');
    }

    public function letsStart()
    {
        $accessToken = session()->get('cutk');
        $url = \Config::get("aireadiness.apis.getrole");
        $jobroles = getWithBearerToken($url, $accessToken);
        return view('cms::letsstart')->with("jobroles",$jobroles);exit;
    }

    public function postLetsStart(Request $request){
        $data =  $request->all();
        // return $data;
        $url = \Config::get("aireadiness.apis.save_profile");
        $accessToken = session()->get('cutk');
        $reqdata = [
            'job_role_id' => $data['profile'],
            'current_role' => 'Full Stack Engineer',
            'exp_in_year' => $data['experience'],
            'primary_skill' => $data['primary-skill'],
            'secondary_skill' => $data['secondary-skill'],
            'is_ai_used'   => $data['ai-tools']
        ];
        $response = postWithBearerToken($url, $accessToken, $reqdata);
        //p($response);
        $assessment_id = $response['data']['assessment_id'];
        session()->put('assessment_id', $assessment_id);
        if($assessment_id){
            return [$assessment_id];
        }else{
            $this->response($response,400,'Profile api failed');
        }
    }

    public function stepForm()
    {
        $accessToken = session()->get('cutk');
        $assessment_id = session()->get('assessment_id');
        $url = \Config::get("aireadiness.apis.start_chat");
        $data['assessment_id'] = $assessment_id;
        $assessmentQuestion = postWithBearerToken($url, $accessToken, $data);
        $response['data'] = [];
        $response['question_messages'] = \Config::get("aireadiness.question_messages");
        $response['skip_messages'] = \Config::get("aireadiness.skip_messages");
        if(isset($assessmentQuestion['status']) && $assessmentQuestion['status']!=200){
            $response['data'] = $assessmentQuestion['data'];
            $response['error'] = $assessmentQuestion['data']['message'];
            $json_error = json_decode(@$assessmentQuestion['data']['raw_response']);
            $response['custom_error'] = (@$json_error->error) ? @$json_error->error : $assessmentQuestion['data']['message'];
            //p($response);
        }
        else{
            $response['data'] = $assessmentQuestion['data'];
        }

        //p($response);
        return view('cms::stepform')
                ->with("assessmentQuestion", $assessmentQuestion['data'])
                ->with("response", $response);
                exit;
    }

    public function postStepForm(Request $request){
        $data =  $request->all();
        $level = 1;
        if(in_array($data['cq'],[3,7,12])){
            $url = \Config::get("aireadiness.apis.save_coding");
            if($data['cq']==7){
                $level = 2;
            }
            if($data['cq']==12){
                $level = 3;
            }
        }else{
            $url = \Config::get("aireadiness.apis.save_mcq");
        }
        $accessToken = session()->get('cutk');
        $assessment_id = session()->get('assessment_id');
        $reqdata = [
            'answer' => $data['answer'],
            'level' => $level,
            'question_id' => $data['question_id'],
            'assessment_id' => $assessment_id,
            'time_taken' => $data['time_taken']
        ];
        $response = postWithBearerToken($url, $accessToken, $reqdata); 
        //p($response);
        if(isset($response['status']) && $response['status']==200){
            return $this->response($response['data'],200,'next question detail');
        }else{
            return $this->response($response,400,'submit answer failed');
        }
    }

    public function assessmentReport($id)
    {
        $assessment_id  = base64_decode($id);
        //$assessment_id = session()->get('assessment_id');
        $url = \Config::get("aireadiness.apis.assessment_report");
        $data['assessment_id'] = $assessment_id;
        $accessToken = session()->get('cutk');
        $assessmentReportDetail = postWithBearerToken($url, $accessToken, $data);
        \Log::info('report data ::'.@json_encode(@$assessmentReportDetail));
        return view('cms::assessment_report')->with("assessmentReportDetail",$assessmentReportDetail['data']);exit;
    }


    public function pageNotFound()
    {
        $response = array();
        return 'page not found';
        return view('cms::common.PageNotFound')->with($response);exit;
    }

    public function clearSessionKey($key)
    {   
        if (Session::has($key))
        {
            Session::forget($key);
        }
    }

    public function logout(){
        Auth::logout();
        session()->forget(['cutka','ubal','playedGameId','uvisit']);
        Cookie::queue(Cookie::forget('uvisit'));
        Cookie::queue(Cookie::forget('cutka'));
        Cookie::queue(Cookie::forget('cutk'));
        Cookie::queue(Cookie::forget('ubal'));
        Cookie::queue(Cookie::forget('playedGameId'));
        Session::flush();
        return redirect('/');
    }
   

}
