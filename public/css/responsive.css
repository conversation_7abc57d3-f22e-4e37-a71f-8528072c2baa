@media (max-width:1266px) {
    .icon-box::before {
        width: 162%;
        right: -192%;
    }

    .cnnectingLine::after {
        right: -200%;
    }
}

@media (min-width:786px) {
    .testimonial-slider .owl-dot {
        width: 20px;
        height: 20px;
        background: #D6D6D6;
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s;
    }

    .testimonial-slider .owl-dot.active {
        background: var(--textColor) !important;
    }

    .testimonial-slider .owl-dots {
        gap: 14px;
        padding: 30px 0 5px;

    }

    .icon-box::before {
        content: "";
        display: inline-block;
        width: 210%;
        height: 1px;
        background-color: #D6D6D6;
        position: absolute;
        right: -240%;
    }

    .icon-box::after {
        content: "";
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: #D6D6D6;
        position: absolute;
        right: -30%;
        border-radius: 50%;
    }

    .cnnectingLine::after {
        content: "";
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: #D6D6D6;
        position: absolute;
        right: -249%;
        border-radius: 50%;
        top: 50%;
        transform: translateY(-50%);
    }

    .noCnnctingLine::before,
    .noCnnctingLine::after {
        display: none;
    }

    .inner-pathway-container>div:first-child,
    .inner-invest-container>div:first-child {
        padding-top: 15px;
    }

    .mt_5 {
        margin-top: -48px;
    }

}

@media (max-width: 996px) {

    .report-desktop {
        display: none;

    }

    .assessment-wrapper main {
        margin-top: 34px;
    }

    .report-mobile {
        display: block;
        width: 86%;
        margin: 0 auto;
        margin-left: 6px;
    }

    .report {
        width: 50%;
        bottom: 40px;
    }

    .report-container {
        margin: 30px 0 46px;
    }

    .assessment-content h1 {
        font-size: 20px;
    }

    .report h3 {
        font-size: 6rem;
    }

    .report h4 {
        font-size: 14px;
        margin: 12px 0 8px;
    }

    .report h5 {
        font-size: 12px;
    }

    .label-container {
        display: flex;
    }

}

@media (max-width: 786px) {

    :root {
        font-size: 8px;
    }

    .wrapper {
        padding: 0 15px;
        /* height: auto; */
    }

    .wrapper .wrp_container {
        padding: 34px 0;
    }

    .wrapper header {
        position: relative;
        justify-content: center;
        display: flex;
        top: unset;
        left: unset;
        transform: unset;
    }

    .main_content_container,
    .form-wrapper {
        padding: 20px;
    }

    .wrapper main {
        margin-top: 22px;
        display: block;
        height: auto;
    }

    .form-wrapper h2 {
        text-align: center;
        font-size: 20px;
    }

    .form-wrapper .subheading {
        text-align: center;
        font-size: 14px;
    }

    .form-grid {
        gap: 22px;
        grid-template-columns: repeat(auto-fit, minmax(100%, 1fr));
    }

    .form-radio-grp {
        margin-top: 14px;
    }

    .form-group input,
    .form-group select {
        height: 53px;
    }

    .form-group select {
        background-size: 30px;
    }

    .form-group .radio-option input[type="radio"] {
        transform: scale(0.4);
    }

    .form-footer {
        flex-direction: column;
        gap: 20px;
        align-items: center;
        margin-top: 14px;
    }

    .card-container {
        flex-direction: column;
    }

    .assessment-container {
        width: 100%;
        padding: 20px;
        box-shadow: 0px 3px 5px #1E497B33;

    }

    .tabs {
        gap: 10px;
        justify-content: space-between;
    }

    .tab {
        padding: 0;
        padding-bottom: 22px;
        font-size: 12px;
    }

    .card {
        padding: 20px;
    }

    .assessment-container .btn {
        margin: 0;
        padding: 10px 26px;
    }

    .options-wrapper {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .explanation-box {
        margin-top: 12px;
        padding: 20px;
    }

    /* Home page responsive Design styling*/

    header.home-header nav {
        display: none;
    }

    header.home-header {
        padding: 20px 0 0;
    }

    .inner-hero-section h1,
    .inner-hero-section p,
    .pathway-section p,
    .inner-invest-container p,
    .program-designed-section .inner-pathway-container .left,
    .testimonial-left h2,
    .partner-left h2 {
        text-align: center;
    }

    .inner-hero-section {
        flex-direction: column;
        padding: 0;
    }

    .inner-hero-section .left {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .inner-hero-section .right {
        margin-top: 30px;
    }

    .inner-hero-section .right img {
        width: 100%;
    }

    .assessment-section,
    .assess-your-skill-section,
    .pathway-section,
    .testimonial-section,
    .partner-section,
    .industry-experts-section {
        padding: 40px 0;
    }

    .inner-invest-container,
    .inner-pathway-container,
    .program-designed-section .inner-pathway-container {
        padding-top: 40px;
        flex-direction: column;
        gap: 30px;
    }

    .inner-invest-container>div,
    .inner-pathway-container>div {
        justify-content: center;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .data-card {
        display: none
    }

    .data-card>div {}

    .score-bg {
        display: none;
    }

    .journey-container {
        flex-direction: column;
        align-items: center;
    }

    .line-dot,
    .user-info {
        display: none;
    }

    .partner-container {
        flex-direction: column;
        text-align: center;
    }

    .logo-grid {
        grid-template-columns: repeat(2, minmax(100px, 200px));
        gap: 30px 0px;
    }

    .assessment-section .cards-container{
        padding-top: 35px;
        overflow: hidden;
    }

    .assessment-section .info-card {
        flex-direction: column;
        position: relative;
        justify-content: center;
        height: 190px;
    }

    .assessment-section .owl-carousel .owl-stage-outer{
        overflow: visible !important;
    }

    .assessment-section .info-card .info-content{
        padding: 0;
        width: 100%;
        padding-top: 25px;
    }

        .card-icon-box {
        width: 70px;
        height: 70px;
        padding: 16px;
        position: absolute;
        top: -35px;
    }

    .assessment-section h3{
        font-size: 16px;
        text-align: center;
    }

    .assessment-section p{
        font-size: 12px;
        text-align: center;

    }

    .desktopView {
        display: none;
    }

    .mobileView {
        display: block;
    }

    .logo-grid img {
        width: 130px;
    }

    .footer-container {
        gap: 10px;
        padding: 15px 0;
    }

    .middle-text {
        font-size: 12px;
        text-align: left;
        margin: 0;
    }

    .middle-text img {
        display: none;
    }

    .banner {
        padding: 15px 0;
        text-align: center;
    }

    .inner-banner-container .ctabtn {
        font-size: 10px;
        width: 158px;
        padding: 15px;
    }

    .free-badge {
        width: 60px;
        height: 60px;
        font-size: 12px;
    }

    .testimonial-left p {
        margin-bottom: 0;
    }

    .testimonial-container {
        gap: 28px;
    }

    .mob-data-container {
        display: flex;
        flex-direction: column;
        gap: 14px;
        padding: 12px 0 40px;
    }

    .mob-data-container>div {
        background: #fff;
        box-shadow: 0px 3px 2px #1E497B0D;
        border-radius: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 25px;
    }

    .mob-data-container h3 {
        color: #000000;
        font-weight: bold;
        font-size: 14px;
    }

    .mob-data-container p {
        color: #000000;
        font-weight: 400;
        font-size: 12px;
        margin-top: 9px;
    }

    .mob-data-container img {
        width: 105px;
    }

    .journey-step {
        height: 252px;
    }

    .icon-box {
        width: 77px;
        height: 77px;
        padding: 20px;
    }

    .journey-step h3 {
        margin: 29px 0 14px;
        font-size: 16px;
    }

    .journey-step p {
        font-size: 14px;
    }

    .journey-container .owl-dots {
        padding: 10px 0 5px;
    }

    .common-container{
        border-radius: 10px;
    }

    .top-header-content {
        flex-direction: column-reverse;
        margin: 0;
    }

    .levels-sec {
        flex-direction: column-reverse;
        gap: 0px;
    }

    .levels {
        gap: 12px;
    }

    .top-header-content .alarm-sec {
        margin: 22px 0 16px;
        width: 128px;
        height: 36px;
        font-size: 20px;
    }

    .top-header-content .alarm-sec img {
        width: 15.5px;
    }

    .question-header {
        width: 100%;
    }

    .level input[type="radio"],
    .level.completed::before {
        width: 19px;
        height: 19px;
    }

    .level input[type="radio"]::before {
        width: 9.5px;
        height: 9.5px;
    }

    .levels {
        font-size: 14px;
    }

    .testimonial-left p{
        text-align: center;
    }

    .line-br{
        display: inline;
    }
}

@media (max-width:500px) {

    .common-container {
        padding: 20px;
    }

    .main_content_container .subtitle span,
    .main_content_container p.terms span,
    .form-wrapper .subheading span {
        display: block;
    }

    .main_content_container .btn_container a {
        font-size: 12px;
        height: 58px;
    }

    .completion-box {
        padding: 80px 40px;
    }

    .completion-box p span {
        display: inline;
    }

    .completion-box h2 {
        font-size: 20px;
    }

    .unlock-box img {
        width: 70%;
    }

    .unlock-label {
        font-size: 8px;
    }

    .unlock-box h3 {
        font-size: 20px;
    }

    .submit-btn,
    .next-btn,
    .unlock-box button,
    .otp-box button {
        font-size: 12px;
        padding: 14px 32px;
    }

    .completion-box img {
        width: 74px;
        margin-bottom: 16px;
    }

    .otp-box img {
        width: 52px;
    }

    .otp-box h2 {
        font-size: 20px;
    }

    .otp-box p {
        font-size: 14px;
        display: inline-block;
        margin-bottom: 20px;
    }

    .form-container {
        width: 100%;
        margin-bottom: 24px;
    }

    .otp-box,
    .unlock-box {
        padding: 50px 20px;
    }

    .resend-container,
    .otp-box .resend {
        margin-top: 24px;
    }

    .otp-box .edit-icon img {
        width: 11px;
    }

    .otp-box .otp-error-msg img {
        width: 14px;
    }

    .question-header {
        margin-bottom: 10px;
    }

    .question-header h3 {
        font-size: 12px;
    }

    .question-title {
        font-size: 14px;
    }

    .skip-next {
        flex-wrap: wrap;
        flex-direction: column;
        gap: 28px;
    }

    .option-label {
        width: 31px;
        height: 31px;
    }

    .option {
        gap: 16px;
    }

    .report-mobile {

        width: 96%;
        margin: 0 auto;
        margin-left: -10px;
    }

    .report {
        width: 68%;
        bottom: 0;
    }

    /* Home page responsive styling */

    header.home-header a img {
        width: 85px;
    }

    .inner-hero-section h1 {
        font-size: 28px;
        line-height: 32px;
    }

    .inner-hero-section h1 img {
        margin: 0 2px 0 12px;
        width: 28px;
    }

    .inner-hero-section p {
        font-size: 13px;
        margin: 28px 0;
    }

    .line-br {
        display: inline;
    }

    .free-assessment {
        gap: 8px;
        margin-top: 25px;
    }

    .free-assessment span {
        font-size: 12px;
    }

    .free-assessment img {
        width: 29px;
    }

    .inner-hero-section .right {
        margin-top: 16px;
    }

    .section-title {
        font-size: 20px;
        line-height: 32px;
        margin-bottom: 20px;
    }

    .assessment-section,
    .assess-your-skill-section,
    .pathway-section,
    .testimonial-section,
    .partner-section,
    .industry-experts-section {
        padding: 24px 0;
    }

    .ctabtn {
        font-size: 12px;
        padding: 16px 46px;
    }

    .assessment-section .info-card {
        padding: 25px;
    }

    .assessment-section .card-title {
        margin: 16px 0;
    }

    .assessment-section .card-list li {
        font-size: 12px;
    }



    .inner-invest-container,
    .inner-pathway-container,
    .program-designed-section .inner-pathway-container {
        padding: 20px 30px;
        gap: 0;
    }

    .inner-invest-container {
        padding-bottom: 0;
    }

    .inner-invest-container h4,
    .pathway-section h4 {
        font-size: 18px;
    }

    .inner-invest-container p,
    .pathway-section p {
        font-size: 14px;
        margin: 20px 0;
    }

    .inner-invest-container img,
    .pathway-section img {
        width: 100%;
    }

    .pathway-section {
        padding: 0;
    }

    .footer-container p,
    .footer-container a {
        font-size: 8px;
    }

    .testimonial-left p,
    .partner-left p {
        font-size: 14px;
    }

    .testimonial-left p {
        text-align: center;
    }

    .testimonial-card {
        padding: 28px;
    }

    .testimonial-left,
    .testimonial-right,
    .partner-left {
        min-width: 100%;
    }

    .inner-invest-container {
        margin-top: 20px;
    }

    .partner-left p {
        margin: 20px 0 24px;
    }

    .partner-left p strong {
        font-weight: 400;
    }

    .partner-container {
        gap: 10px;
    }

    .inner-invest-container .ctabtn {
        margin: 6px 0 26px;
    }

    .assessment-section .info-card,
    .journey-step {
        min-width: 100%;
    }

    .journey-step {
        padding: 20px 0;
    }

    .inner-pathway-container,
    .program-designed-section .inner-pathway-container {
        padding: 20px 0;
        text-align: center;
    }

    .inner-invest-container h4 {
        text-align: center;
    }

    .assess-your-skill-section .highlight::after {
        background-size: contain;
        width: 100%;
        left: 0;
    }

    #toast {
        text-align: center;
        width: 85%;
        padding: 10px;
    }

    main.hero-section {
        background: url(../img/mobile-hero-bg.png) 100% 96%;
        background-size: cover;
    }

    .assessment-section .card-list li::before {
        font-size: 1.5rem;
        top: 0px;
    }
    .inner-banner-container{
        gap: 10px;
    }
}

@media (max-width:340px) {

    .main_content_container p.terms span,
    .form-wrapper .subheading span {
        display: inline;
    }

    .completion-box {
        padding: 80px 20px;
    }

    .form-group .radio-option {
        width: calc(50% - 10px);
    }

    .levels {
        gap: 10px;
    }

    .level input[type="radio"] {
        width: 16px;
        height: 16px;
    }

    .level input[type="radio"]::before {
        width: 8px;
        height: 8px;
    }

    .levels label {
        gap: 4px;
    }

    .report h4 {
        margin: 4px 0 8px;
    }

    .label-container {
        gap: 22px 10px;
        padding-top: 16px;
    }

    .inner-hero-section h1 span {
        display: inline;
    }

    .inner-hero-section h1 {
        font-size: 23px;
    }

    .section-title {
        font-size: 16px;
        line-height: 26px;
    }

    .mob-data-container img {
        width: 66px;
    }

    .mob-data-container>div {
        padding: 16px;
    }

    .ctabtn {
        font-size: 10px;
        padding: 16px 7px;
        width: 200px;
        text-align: center;
    }


}

@media (max-width:300px) {
    .report {
        bottom: -32px;
    }

    .assessment-content h1 {
        font-size: 17px;
    }
    .levels {
        font-size: 12px;
    }
}

@media (max-height:800px) {
    .h-none {
        height: auto;
    }

    .wrapper header {
        position: relative;
        display: flex;
        top: unset;
        left: unset;
        transform: unset;
    }

    .wrapper main {
        margin-top: 22px;
        display: block;
        height: auto;
    }

    .login-wrapper header {
        position: absolute;
    }

    .login-wrapper main {
        width: 100%;
        height: 100%;
        display: grid;
        place-items: center;
        margin: 0;
    }
}

/* @media (max-height:600px) and (min-width:787px) {
    .h-none {
        height: auto;
    }

    .wrapper header {
        position: relative;
        display: flex;
        top: unset;
        left: unset;
        transform: unset;
    }

    .wrapper main {
        margin-top: 34px;
        display: block;
        height: auto;
    }
} */

@media (max-height:550px) {
    .h-none-new {
        height: auto;
    }

    .h-none {
        height: auto;
    }

    .wrapper {
        height: auto;
    }

    .wrapper main {
        padding-bottom: 10px;
    }
}

@media (max-width:786px) and (min-height:520px) {
    .login-wrapper {
        height: 100vh !important;
    }

    .login-wrapper main {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        margin: 0 !important;
    }

    .login-wrapper header {
        position: absolute;
        width: 100%;
        left: 50%;
        transform: translate(-50%);
    }
}